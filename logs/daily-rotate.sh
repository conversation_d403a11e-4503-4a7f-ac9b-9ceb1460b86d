#!/bin/bash

# Daily log rotation script for Eko Backend
# This script aggregates Docker logs into daily files: eko-YYYY-MM-DD.log
# Run this daily via cron: 0 0 * * * /path/to/daily-rotate.sh

set -euo pipefail

LOG_DIR="$(dirname "$0")"
DATE_TODAY=$(date +%Y-%m-%d)
DATE_YESTERDAY=$(date -d "yesterday" +%Y-%m-%d)
RETENTION_DAYS=30

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Create daily log file from Docker logs
create_daily_log() {
    local date_str="$1"
    local output_file="$LOG_DIR/eko-${date_str}.log"
    
    log "Creating daily log for $date_str"
    
    # Get all eko-related containers
    local containers=$(docker ps -a --format "{{.Names}}" | grep -E "eko-api|traefik-api" || true)
    
    if [ -z "$containers" ]; then
        warn "No eko containers found"
        return 1
    fi
    
    # Create header
    {
        echo "=== EKO BACKEND DAILY LOG - $date_str ==="
        echo "Generated at: $(date)"
        echo "Containers: $(echo $containers | tr '\n' ' ')"
        echo "=============================================="
        echo ""
    } > "$output_file"
    
    # Aggregate logs from each container for the specific date
    for container in $containers; do
        {
            echo "--- CONTAINER: $container ---"
            # Get logs for specific date range
            docker logs "$container" \
                --since "${date_str}T00:00:00" \
                --until "${date_str}T23:59:59" \
                2>&1 | while IFS= read -r line; do
                    echo "$container | $line"
                done || echo "No logs found for $container on $date_str"
            echo ""
        } >> "$output_file"
    done
    
    log "Daily log created: $output_file ($(du -h "$output_file" | cut -f1))"
}

# Cleanup old logs
cleanup_old_logs() {
    log "Cleaning up logs older than $RETENTION_DAYS days"
    
    find "$LOG_DIR" -name "eko-*.log" -mtime +$RETENTION_DAYS -delete 2>/dev/null || true
    
    log "Cleanup completed"
}

# Main execution
main() {
    cd "$LOG_DIR"
    
    # Create yesterday's log if it doesn't exist
    if [ ! -f "eko-${DATE_YESTERDAY}.log" ]; then
        create_daily_log "$DATE_YESTERDAY"
    fi
    
    # Cleanup old logs
    cleanup_old_logs
    
    # Show current log files
    log "Current log files:"
    ls -lh eko-*.log 2>/dev/null | tail -5 || echo "No log files found"
}

# Run if executed directly
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
