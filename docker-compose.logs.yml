# Loki + <PERSON>ana Centralized Logging Stack
# Usage: docker-compose -f docker-compose.yml -f docker-compose.logs.yml up -d
# Access Grafana at: http://localhost:8203 (admin/admin123)

version: '3.8'

services:
  # Loki - Log aggregation with daily rotation
  loki:
    image: grafana/loki:2.9.0-amd64
    container_name: eko-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./logs:/var/log/eko  # Host directory for log files
      - loki-data:/loki
    environment:
      - LOKI_CONFIG_FILE=/etc/loki/local-config.yaml
    networks:
      - eko-network
    mem_limit: 100m
    cpus: 0.2
    configs:
      - source: loki_config
        target: /etc/loki/local-config.yaml

  # Grafana - Lightweight dashboard
  grafana:
    image: grafana/grafana:10.2.0-ubuntu
    container_name: eko-grafana
    restart: unless-stopped
    ports:
      - "8203:3000"  # Dedicated port as requested
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=
      - GF_ANALYTICS_REPORTING_ENABLED=false
      - GF_ANALYTICS_CHECK_FOR_UPDATES=false
      - GF_SECURITY_DISABLE_GRAVATAR=true
    volumes:
      - grafana-data:/var/lib/grafana
    networks:
      - eko-network
    mem_limit: 80m
    cpus: 0.1
    depends_on:
      - loki
    configs:
      - source: grafana_datasource
        target: /etc/grafana/provisioning/datasources/loki.yaml
      - source: grafana_dashboard_config
        target: /etc/grafana/provisioning/dashboards/dashboard.yaml
      - source: grafana_dashboard
        target: /var/lib/grafana/dashboards/eko-logs.json

  # Promtail - Log collector with file output
  promtail:
    image: grafana/promtail:2.9.0-amd64
    container_name: eko-promtail
    restart: unless-stopped
    volumes:
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./logs:/var/log/eko  # Same host directory for file output
    command: -config.file=/etc/promtail/config.yml
    networks:
      - eko-network
    mem_limit: 50m
    cpus: 0.1
    depends_on:
      - loki
    configs:
      - source: promtail_config
        target: /etc/promtail/config.yml

configs:
  # Loki configuration with daily rotation and file output
  loki_config:
    content: |
      auth_enabled: false
      
      server:
        http_listen_port: 3100
        grpc_listen_port: 9096
        log_level: warn
      
      common:
        path_prefix: /loki
        storage:
          filesystem:
            chunks_directory: /loki/chunks
            rules_directory: /loki/rules
        replication_factor: 1
        ring:
          instance_addr: 127.0.0.1
          kvstore:
            store: inmemory
      
      query_range:
        results_cache:
          cache:
            embedded_cache:
              enabled: true
              max_size_mb: 50
      
      schema_config:
        configs:
          - from: 2020-10-24
            store: boltdb-shipper
            object_store: filesystem
            schema: v11
            index:
              prefix: index_
              period: 24h
      
      # Daily retention and cleanup
      limits_config:
        retention_period: 30d
        ingestion_rate_mb: 8
        ingestion_burst_size_mb: 16
        max_query_series: 500
      
      compactor:
        working_directory: /loki/compactor
        shared_store: filesystem
        compaction_interval: 10m
        retention_enabled: true
        retention_delete_delay: 2h
      
      table_manager:
        retention_deletes_enabled: true
        retention_period: 30d

  # Promtail configuration for Docker logs + file output
  promtail_config:
    content: |
      server:
        http_listen_port: 9080
        grpc_listen_port: 0
        log_level: warn
      
      positions:
        filename: /tmp/positions.yaml
      
      clients:
        - url: http://loki:3100/loki/api/v1/push
      
      scrape_configs:
        # Docker container logs
        - job_name: docker
          static_configs:
            - targets:
                - localhost
              labels:
                job: docker
                __path__: /var/lib/docker/containers/*/*-json.log
          
          pipeline_stages:
            - json:
                expressions:
                  output: log
                  stream: stream
                  attrs: attrs
                  time: time
            
            - json:
                expressions:
                  tag: tag
                source: attrs
            
            - regex:
                expression: (?P<container_name>(?:[^|]*))
                source: tag
            
            - labels:
                stream:
                container_name:
            
            # Filter only eko services
            - match:
                selector: '{container_name=~".*eko-api.*|.*traefik.*"}'
                action: keep
            
            # Add instance info
            - template:
                source: instance_info
                template: '{{ .container_name }}'
            
            - labels:
                instance_info:
            
            - timestamp:
                source: time
                format: RFC3339Nano
            
            # Output to file with daily rotation
            - template:
                source: output_file
                template: '/var/log/eko/eko-{{ .timestamp | date "2006-01-02" }}.log'
            
            # Write to daily log file
            - match:
                selector: '{job="docker"}'
                stages:
                  - output:
                      source: output

  # Grafana datasource configuration
  grafana_datasource:
    content: |
      apiVersion: 1
      datasources:
        - name: Loki
          type: loki
          access: proxy
          url: http://loki:3100
          isDefault: true
          editable: false

  # Grafana dashboard provisioning
  grafana_dashboard_config:
    content: |
      apiVersion: 1
      providers:
        - name: 'eko-logs'
          orgId: 1
          folder: ''
          type: file
          disableDeletion: false
          updateIntervalSeconds: 10
          allowUiUpdates: true
          options:
            path: /var/lib/grafana/dashboards

  # Grafana dashboard for Eko logs
  grafana_dashboard:
    content: |
      {
        "dashboard": {
          "id": null,
          "title": "Eko Backend Logs",
          "tags": ["eko", "logs"],
          "timezone": "browser",
          "panels": [
            {
              "id": 1,
              "title": "Live Logs by Instance",
              "type": "logs",
              "targets": [
                {
                  "expr": "{job=\"docker\", container_name=~\".*eko.*|.*traefik.*\"}",
                  "refId": "A"
                }
              ],
              "gridPos": {"h": 12, "w": 24, "x": 0, "y": 0},
              "options": {
                "showTime": true,
                "showLabels": true,
                "sortOrder": "Descending"
              }
            },
            {
              "id": 2,
              "title": "Log Volume by Service",
              "type": "stat",
              "targets": [
                {
                  "expr": "sum by (container_name) (count_over_time({job=\"docker\", container_name=~\".*eko.*|.*traefik.*\"}[5m]))",
                  "refId": "A"
                }
              ],
              "gridPos": {"h": 6, "w": 12, "x": 0, "y": 12}
            },
            {
              "id": 3,
              "title": "Error Logs",
              "type": "logs",
              "targets": [
                {
                  "expr": "{job=\"docker\", container_name=~\".*eko.*|.*traefik.*\"} |~ \"(?i)error|exception|failed|panic\"",
                  "refId": "A"
                }
              ],
              "gridPos": {"h": 6, "w": 12, "x": 12, "y": 12}
            }
          ],
          "time": {"from": "now-1h", "to": "now"},
          "refresh": "5s"
        }
      }

volumes:
  loki-data:
    driver: local
  grafana-data:
    driver: local

networks:
  eko-network:
    external: true
