# Simple logging solution - just add this to your existing setup
# Usage: docker-compose -f docker-compose.yml -f docker-compose.logs.yml up -d

version: '3.8'

services:
  # Dozzle - Real-time log viewer with web dashboard
  dozzle:
    container_name: dozzle
    image: amir20/dozzle:latest
    restart: unless-stopped
    ports:
      - "9999:8080"  # Access logs at http://localhost:9999
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./logs:/logs  # Optional: for file exports
    environment:
      - DOZZLE_LEVEL=info
      - DOZZLE_TAILSIZE=300
      - DOZZLE_FILTER="name=eko-api,name=eko-api-docs,name=traefik-api"
      - DOZZLE_ENABLE_ACTIONS=true
    networks:
      - eko-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dozzle.rule=Host(`${DOMAIN}`) && PathPrefix(`/logs`)"
      - "traefik.http.routers.dozzle.entrypoints=websecure,http-api"
      - "traefik.http.routers.dozzle.service=dozzle-service"
      - "traefik.http.routers.dozzle.middlewares=api-cors@file"
      - "${DOMAIN:+traefik.http.routers.dozzle.tls=true}"
      - "traefik.http.services.dozzle-service.loadbalancer.server.port=8080"

networks:
  eko-network:
    external: true
