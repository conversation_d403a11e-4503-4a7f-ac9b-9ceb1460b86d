import logging
import logging.handlers
import sys
import shutup
import warnings
import os
import multiprocessing
import subprocess
import functools
import time
import traceback
import inspect
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Callable, Dict, Any, List
from tabulate import tabulate

class ProcessInfoMixin:
    @staticmethod
    def get_supervisor_info():
        try:
            proc_name = os.environ.get('SUPERVISOR_PROCESS_NAME', 'Unknown')
            parent_pid = os.getppid()
            ps_output = subprocess.check_output(['ps', '-p', str(parent_pid), '-o', 'comm=']).decode().strip()
            return f"{proc_name}|{ps_output}"
        except Exception:
            return "Unknown"

class SubtleGradientColoredFormatter(logging.Formatter):
    COLORS = {
        "DEBUG": ["\033[94;1m"],
        "INFO": ["\033[92m"],
        "WARNING": ["\033[93m"],
        "ERROR": ["\033[91m"],
        "CRITICAL": ["\033[95m"],
    }
    RESET = "\033[0m"

    def format(self, record):
        level_colors = self.COLORS.get(record.levelname, [self.RESET])
        color_index = min(record.levelno // 10, len(level_colors) - 1)
        gradient_color = level_colors[color_index]

        record.process_info = ProcessInfoMixin.get_supervisor_info()
        record.file_info = os.path.basename(record.pathname) if hasattr(record, 'pathname') else "unknown"

        message = super().format(record)
        return f"{gradient_color}{message}{self.RESET}"

class CustomLoggerAdapter(logging.LoggerAdapter, ProcessInfoMixin):
    def __init__(self, logger, logger_name):
        super().__init__(logger, {})
        self.logger_name = logger_name
        self.process_name = multiprocessing.current_process().name

    def process(self, msg, kwargs):
        extra = kwargs.pop('extra', {})
        msg = f"{self.logger_name} - {msg}"
        return msg, kwargs

    def log_dict_as_table(self, data: Dict[str, Any], title: str = None, headers=["Key", "Value"], tablefmt="rounded_grid"):
        """Log dictionary data as a formatted table.

        Args:
            data: Dictionary to be logged
            title: Optional title for the table
            headers: Table headers (default: ["Key", "Value"])
            tablefmt: Table format (default: grid)
        """
        def safe_str_convert(value):
            """Safely convert any value to string for tabulate"""
            try:
                if isinstance(value, bool):
                    return str(value)
                elif isinstance(value, (dict, list)):
                    return str(value)
                elif value is None:
                    return "None"
                else:
                    return str(value)
            except Exception as e:
                return f"<Error converting value: {type(value).__name__}>"

        # Convert all data to safe strings
        safe_table_data = []
        for k, v in data.items():
            try:
                key_str = str(k)
                value_str = safe_str_convert(v)
                safe_table_data.append([key_str, value_str])
            except Exception as e:
                safe_table_data.append([str(k), f"<Error: {e}>"])

        table = tabulate(safe_table_data, headers=headers, tablefmt=tablefmt, maxcolwidths=[None, 100])
        if title:
            self.info(f"\n{title}:\n{table}")
        else:
            self.info(f"\n{table}")

    def log_list_of_dicts(self, data: List[Dict[str, Any]], title: str = None, headers="keys", tablefmt="grid"):
        """Log list of dictionaries as a formatted table.
        
        Args:
            data: List of dictionaries to be logged
            title: Optional title for the table
            headers: Table headers (default: dictionary keys)
            tablefmt: Table format (default: grid)
        """
        if not data:
            self.warning("Empty data list provided for table logging")
            return
            
        table = tabulate(data, headers=headers, tablefmt=tablefmt)
        if title:
            self.info(f"\n{title}:\n{table}")
        else:
            self.info(f"\n{table}")

def setup_centralized_logging(logger_name, log_dir="/var/log/eko", instance_name=None):
    """
    Setup centralized logging with daily rotation and instance-wise separation.

    Args:
        logger_name: Name of the logger
        log_dir: Directory to store log files (default: /var/log/eko)
        instance_name: Instance identifier (auto-detected from environment if not provided)

    Features:
        - Daily log rotation (eko-YYYY-MM-DD.log)
        - Size-based rotation when files get too large (eko-YYYY-MM-DD-1.log, -2.log, etc.)
        - Instance-wise logging (eko-instance1-YYYY-MM-DD.log)
        - Restart handling (appends to existing daily logs)
        - Both file and console output
    """
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.DEBUG)

    if logger.hasHandlers():
        logger.handlers.clear()

    # Auto-detect instance name from environment variables
    if not instance_name:
        instance_name = (
            os.environ.get('INSTANCE_ID') or
            os.environ.get('HOSTNAME') or
            os.environ.get('SERVICE_NAME') or
            f"instance-{os.getpid()}"
        )

    # Ensure log directory exists
    Path(log_dir).mkdir(parents=True, exist_ok=True)

    # Create log formatters
    file_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - [%(funcName)s] - %(process)d - %(pathname)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    console_formatter = SubtleGradientColoredFormatter(
        '%(asctime)s - %(levelname)s - [%(funcName)s] - %(process_info)s - %(file_info)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # Setup daily rotating file handler with instance separation
    log_filename = f"{log_dir}/eko-{instance_name}.log"

    # TimedRotatingFileHandler for daily rotation
    daily_handler = logging.handlers.TimedRotatingFileHandler(
        filename=log_filename,
        when='midnight',
        interval=1,
        backupCount=30,  # Keep 30 days of logs
        encoding='utf-8'
    )
    daily_handler.suffix = "%Y-%m-%d"  # Date format for rotated files
    daily_handler.setLevel(logging.DEBUG)
    daily_handler.setFormatter(file_formatter)

    # Additional size-based rotation to prevent huge files
    size_handler = logging.handlers.RotatingFileHandler(
        filename=f"{log_dir}/eko-{instance_name}-current.log",
        maxBytes=100*1024*1024,  # 100MB per file
        backupCount=5,  # Keep 5 backup files (-1, -2, -3, -4, -5)
        encoding='utf-8'
    )
    size_handler.setLevel(logging.DEBUG)
    size_handler.setFormatter(file_formatter)

    # Console handler for real-time monitoring
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)  # Less verbose on console
    console_handler.setFormatter(console_formatter)

    # Add all handlers
    logger.addHandler(daily_handler)
    logger.addHandler(size_handler)
    logger.addHandler(console_handler)
    logger.propagate = False

    # Log startup information
    adapter = CustomLoggerAdapter(logger, logger_name)
    adapter.info(f"Centralized logging initialized for instance: {instance_name}")
    adapter.info(f"Log directory: {log_dir}")
    adapter.info(f"Daily logs: {log_filename} (rotated at midnight)")
    adapter.info(f"Size-based logs: {log_dir}/eko-{instance_name}-current.log (100MB rotation)")

    return adapter

def setup_new_logging(logger_name):
    """Backward compatibility wrapper - now uses centralized logging"""
    return setup_centralized_logging(logger_name)

class FunctionLoggingHandler:
    def __init__(self, logger: logging.Logger):
        self.logger = logger
    
    def __call__(self, func: Callable) -> Callable:
        if asyncio.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    self.logger.info(f"Starting: {func.__name__}")
                    result = await func(*args, **kwargs)
                    return result
                except Exception as e:
                    self.logger.error(f"Error in {func.__name__}: {e}")
                    raise
                finally:
                    execution_time = time.time() - start_time
                    time_msg = f"({execution_time:.2f}s)"
                    if execution_time > 1:
                        time_msg = f"\033[91m{time_msg}\033[0m"
                    self.logger.info(f"Completed: {func.__name__} {time_msg}")
            return async_wrapper
        else:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    self.logger.info(f"Starting: {func.__name__}")
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    self.logger.error(f"Error in {func.__name__}: {e}")
                    raise
                finally:
                    execution_time = time.time() - start_time
                    time_msg = f"({execution_time:.2f}s)"
                    if execution_time > 1:
                        time_msg = f"\033[91m{time_msg}\033[0m"
                    self.logger.info(f"Completed: {func.__name__} {time_msg}")
            return wrapper

def apply_logging_to_all_functions(module_globals: dict) -> None:
    logger = setup_new_logging(__name__)
    function_logger = FunctionLoggingHandler(logger)
    
    for name, obj in module_globals.items():
        if name.startswith('__') or not callable(obj) or inspect.isclass(obj):
            continue
        module_globals[name] = function_logger(obj)