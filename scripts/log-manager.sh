#!/bin/bash

# Centralized Log Manager for Eko Backend
# Handles daily log rotation, aggregation, and cleanup
# Usage: ./scripts/log-manager.sh [command]
# Commands: start, stop, rotate, cleanup, tail, follow

set -euo pipefail

# Configuration
LOG_DIR="./logs"
ARCHIVE_DIR="./logs/archive"
RETENTION_DAYS=30
DATE_FORMAT=$(date +%Y-%m-%d)
TIMESTAMP=$(date +%Y-%m-%d_%H-%M-%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# Create log directories
setup_directories() {
    log "Setting up log directories..."
    mkdir -p "$LOG_DIR"
    mkdir -p "$ARCHIVE_DIR"
    
    # Set permissions for Docker containers
    chmod 755 "$LOG_DIR"
    chmod 755 "$ARCHIVE_DIR"
    
    log "Log directories created: $LOG_DIR, $ARCHIVE_DIR"
}

# Aggregate logs from all containers
aggregate_logs() {
    local date_suffix=${1:-$DATE_FORMAT}
    local output_file="$LOG_DIR/eko-${date_suffix}.log"
    
    log "Aggregating logs for date: $date_suffix"
    
    # Get all eko containers
    local containers=$(docker ps --format "table {{.Names}}" | grep -E "eko-api|traefik-api" | tail -n +2 || true)
    
    if [ -z "$containers" ]; then
        warn "No eko containers found running"
        return 1
    fi
    
    # Create/append to daily log file
    {
        echo "=== EKO BACKEND AGGREGATED LOGS - $date_suffix ==="
        echo "Generated at: $(date)"
        echo "Containers: $containers"
        echo "=================================================="
        echo ""
    } >> "$output_file"
    
    # Aggregate logs from each container
    for container in $containers; do
        {
            echo "--- CONTAINER: $container ---"
            docker logs "$container" --since "${date_suffix}T00:00:00" --until "${date_suffix}T23:59:59" 2>&1 || echo "Failed to get logs for $container"
            echo ""
        } >> "$output_file"
    done
    
    log "Logs aggregated to: $output_file"
}

# Rotate logs daily
rotate_logs() {
    log "Starting log rotation..."
    
    # Get yesterday's date for rotation
    local yesterday=$(date -d "yesterday" +%Y-%m-%d)
    
    # Aggregate yesterday's logs if not already done
    if [ ! -f "$LOG_DIR/eko-${yesterday}.log" ]; then
        log "Aggregating logs for $yesterday"
        aggregate_logs "$yesterday"
    fi
    
    # Move large current logs to archive with timestamp
    find "$LOG_DIR" -name "eko-*.log" -size +100M -exec bash -c '
        for file; do
            filename=$(basename "$file" .log)
            mv "$file" "'$ARCHIVE_DIR'/${filename}-'$TIMESTAMP'.log"
            echo "Archived large log: $file"
        done
    ' _ {} +
    
    log "Log rotation completed"
}

# Cleanup old logs
cleanup_logs() {
    log "Cleaning up logs older than $RETENTION_DAYS days..."
    
    # Remove old daily logs
    find "$LOG_DIR" -name "eko-*.log" -mtime +$RETENTION_DAYS -delete
    
    # Remove old archived logs
    find "$ARCHIVE_DIR" -name "*.log" -mtime +$RETENTION_DAYS -delete
    
    # Remove old Docker logs (if using json-file driver)
    docker system prune -f --filter "until=${RETENTION_DAYS}h" > /dev/null 2>&1 || true
    
    log "Cleanup completed"
}

# Follow logs in real-time (like docker-compose logs -f)
follow_logs() {
    log "Following logs from all eko containers..."
    log "Press Ctrl+C to stop"
    
    # Get running containers
    local containers=$(docker ps --format "{{.Names}}" | grep -E "eko-api|traefik-api" || true)
    
    if [ -z "$containers" ]; then
        error "No eko containers found running"
        exit 1
    fi
    
    # Follow logs from all containers with timestamps and container names
    docker-compose logs -f --timestamps eko-api eko-api-docs traefik 2>/dev/null || \
    docker logs -f $(echo $containers | tr ' ' '\n' | head -1) 2>/dev/null
}

# Tail recent logs
tail_logs() {
    local lines=${1:-100}
    log "Showing last $lines lines from today's logs..."
    
    local today_log="$LOG_DIR/eko-${DATE_FORMAT}.log"
    
    if [ -f "$today_log" ]; then
        tail -n "$lines" "$today_log"
    else
        warn "No aggregated log found for today. Showing Docker logs instead..."
        docker-compose logs --tail="$lines" eko-api eko-api-docs traefik 2>/dev/null || \
        echo "No logs available"
    fi
}

# Show log statistics
show_stats() {
    log "Log Statistics:"
    echo ""
    
    # Daily logs
    echo -e "${BLUE}Daily Logs:${NC}"
    ls -lh "$LOG_DIR"/eko-*.log 2>/dev/null | awk '{print $9, $5, $6, $7, $8}' || echo "No daily logs found"
    echo ""
    
    # Archived logs
    echo -e "${BLUE}Archived Logs:${NC}"
    ls -lh "$ARCHIVE_DIR"/*.log 2>/dev/null | wc -l | xargs echo "Total archived files:"
    du -sh "$ARCHIVE_DIR" 2>/dev/null | awk '{print "Archive size:", $1}' || echo "No archived logs"
    echo ""
    
    # Docker logs
    echo -e "${BLUE}Running Containers:${NC}"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "eko-api|traefik-api" || echo "No eko containers running"
}

# Install as cron job for daily rotation
install_cron() {
    local script_path=$(realpath "$0")
    local cron_entry="0 0 * * * $script_path rotate && $script_path cleanup"
    
    log "Installing daily log rotation cron job..."
    
    # Add to crontab if not already present
    (crontab -l 2>/dev/null | grep -v "$script_path"; echo "$cron_entry") | crontab -
    
    log "Cron job installed: Daily rotation at midnight"
    log "Current crontab:"
    crontab -l | grep "$script_path" || echo "No cron jobs found"
}

# Main command handler
main() {
    local command=${1:-help}
    
    case "$command" in
        "setup"|"init")
            setup_directories
            ;;
        "start")
            setup_directories
            aggregate_logs
            ;;
        "rotate")
            rotate_logs
            ;;
        "cleanup")
            cleanup_logs
            ;;
        "follow"|"tail-f")
            follow_logs
            ;;
        "tail")
            tail_logs "${2:-100}"
            ;;
        "stats"|"status")
            show_stats
            ;;
        "install-cron")
            install_cron
            ;;
        "aggregate")
            aggregate_logs "${2:-$DATE_FORMAT}"
            ;;
        "help"|*)
            echo "Eko Backend Log Manager"
            echo ""
            echo "Usage: $0 [command] [options]"
            echo ""
            echo "Commands:"
            echo "  setup          - Create log directories"
            echo "  start          - Setup and start log aggregation"
            echo "  rotate         - Rotate logs (run daily)"
            echo "  cleanup        - Remove old logs"
            echo "  follow         - Follow logs in real-time (like docker-compose logs -f)"
            echo "  tail [lines]   - Show recent logs (default: 100 lines)"
            echo "  stats          - Show log statistics"
            echo "  aggregate [date] - Aggregate logs for specific date (YYYY-MM-DD)"
            echo "  install-cron   - Install daily rotation cron job"
            echo ""
            echo "Examples:"
            echo "  $0 start                    # Setup and start logging"
            echo "  $0 follow                   # Follow logs like docker-compose logs -f"
            echo "  $0 tail 500                 # Show last 500 log lines"
            echo "  $0 aggregate 2024-01-15     # Aggregate logs for specific date"
            echo ""
            ;;
    esac
}

# Run main function with all arguments
main "$@"
