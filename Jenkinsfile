pipeline {
    agent any

    environment {
        EKO_API_REPLICAS = '10'
        EKO_DOCS_REPLICAS = '1'
    }

    stages {
        stage('Setup Environment') {
            steps {
                sshagent(['ai_server3']) {
                    withCredentials([file(credentialsId: 'echo_backend_env', variable: 'ENV_FILE')]) {
                        sh '''
                            # Copy .env file to remote server
                            scp "$ENV_FILE" ai_server3:.env
                            
                            # Add scaling configuration
                            ssh ai_server3 "
                                echo 'EKO_API_REPLICAS=${EKO_API_REPLICAS}' >> .env
                                echo 'EKO_DOCS_REPLICAS=${EKO_DOCS_REPLICAS}' >> .env
                                mkdir -p logs config
                            "
                        '''
                    }
                }
            }
        }

        stage('Build & Deploy') {
            steps {
                sshagent(['ai_server3']) {
                    sh '''
                        ssh ai_server3 "
                            # Stop existing services
                            docker compose down || true
                            
                            # Build and deploy with scaling
                            docker compose up -d --build --scale eko-api=${EKO_API_REPLICAS} --scale eko-api-docs=${EKO_DOCS_REPLICAS}
                            
                            # Wait and verify
                            sleep 30
                            docker compose ps
                        "
                    '''
                }
            }
        }

        stage('Verify Deployment') {
            steps {
                sshagent(['ai_server3']) {
                    sh '''
                        ssh ai_server3 "
                            echo '📊 Deployment Status:'
                            API_COUNT=\$(docker ps --filter name=eko-api --filter status=running | grep -c eko-api || echo 0)
                            DOCS_COUNT=\$(docker ps --filter name=eko-api-docs --filter status=running | grep -c eko-api-docs || echo 0)
                            
                            echo 'EKO API instances: \$API_COUNT/${EKO_API_REPLICAS}'
                            echo 'EKO Docs instances: \$DOCS_COUNT/${EKO_DOCS_REPLICAS}'
                            
                            if [ \$API_COUNT -eq ${EKO_API_REPLICAS} ] && [ \$DOCS_COUNT -eq ${EKO_DOCS_REPLICAS} ]; then
                                echo '✅ Deployment successful'
                            else
                                echo '❌ Deployment failed - incorrect instance count'
                                exit 1
                            fi
                        "
                    '''
                }
            }
        }
    }

    post {
        success {
            echo "🎉 Deployment completed: ${EKO_API_REPLICAS} API + ${EKO_DOCS_REPLICAS} Docs instances"
        }
        failure {
            echo "❌ Deployment failed"
        }
        always {
            sshagent(['ai_server3']) {
                sh 'ssh ai_server3 "docker system prune -f"'
            }
        }
    }
}
