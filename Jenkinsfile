pipeline {
    agent any

    stages {
        stage('Server Diagnostics') {
            steps {
                sh '''
                    echo "🌐 Server Information:"

                    echo "External IP: $(curl -s ifconfig.me || echo 'Unable to get external IP')"
                    echo "Internal IP: $(hostname -I | awk '{print $1}' || echo 'Unable to get internal IP')"
                    echo "Hostname: $(hostname)"
                    echo ""

                    echo "🔍 Port 80 Usage:"
                    netstat -tulpn | grep :80 || echo "Port 80 is free"
                    echo ""

                    echo "📋 What's using port 80:"
                    lsof -i :80 || echo "No processes found on port 80"
                    echo ""

                    echo "🔍 Port 443 Usage:"
                    netstat -tulpn | grep :443 || echo "Port 443 is free"
                    echo ""

                    echo "📋 What's using port 443:"
                    lsof -i :443 || echo "No processes found on port 443"
                    echo ""

                    echo "🐳 Docker Status:"
                    docker --version || echo "Docker not available"
                    docker ps --format "table {{.Names}}\t{{.Ports}}" | head -10 || echo "No containers running"
                    
                '''
            }
        }
    }
}

    post {
        always {
            echo "🔚 Pipeline completed."
        }
    }