pipeline {
    agent any

    environment {
        EKO_API_REPLICAS = '10'        // General API instances
        EKO_DOCS_REPLICAS = '1'        // Document processing instances
        EXPOSED_PORT = '8200'
    }

    stages {
        stage('Setup') {
            steps {
                withCredentials([file(credentialsId: 'echo_backend_env', variable: 'ENV_FILE')]) {
                    sh '''
                        # Alternative approach without sudo
                        echo "Setting up environment..."

                        # Use cat instead of cp to avoid permission issues
                        cat "$ENV_FILE" > .env || {
                            echo "Direct write failed, trying alternative..."
                            # If direct write fails, use docker to create the file
                            docker run --rm -v "$PWD":/workspace -v "$ENV_FILE":/source alpine:latest sh -c "
                                cat /source > /workspace/.env
                                echo 'EKO_API_REPLICAS=${EKO_API_REPLICAS}' >> /workspace/.env
                                echo 'EKO_DOCS_REPLICAS=${EKO_DOCS_REPLICAS}' >> /workspace/.env
                                echo 'EXPOSED_PORT=${EXPOSED_PORT}' >> /workspace/.env
                                chmod 644 /workspace/.env
                            "
                        } || {
                            # Last resort: create manually
                            echo "Creating .env manually..."
                            touch .env
                            echo "EKO_API_REPLICAS=${EKO_API_REPLICAS}" > .env
                            echo "EKO_DOCS_REPLICAS=${EKO_DOCS_REPLICAS}" >> .env
                            echo "EXPOSED_PORT=${EXPOSED_PORT}" >> .env
                        }

                        # Ensure logs directory exists
                        mkdir -p logs config

                        echo "✅ Environment setup completed"
                        ls -la .env || echo "Warning: .env file not found"
                    '''
                }
            }
        }

        stage('Deploy with Auto-scaling') {
            steps {
                script {
                    try {
                        sh '''
                            echo "🚀 Starting deployment with scaling..."
                            echo "EKO API instances: ${EKO_API_REPLICAS}"
                            echo "EKO Docs instances: ${EKO_DOCS_REPLICAS}"

                            # Stop existing services
                            docker compose down || true

                            # Build images
                            docker compose build --no-cache

                            # Start with scaling
                            docker compose up -d --scale eko-api=${EKO_API_REPLICAS} --scale eko-api-docs=${EKO_DOCS_REPLICAS}

                            echo "⏳ Waiting for services to start..."
                            sleep 45

                            echo "📊 Checking deployment status..."
                            docker compose ps

                            echo "🔍 Verifying instance counts..."
                            echo "EKO API instances running: $(docker ps --filter name=eko-api --filter status=running | grep -c eko-api || echo 0)"
                            echo "EKO Docs instances running: $(docker ps --filter name=eko-api-docs --filter status=running | grep -c eko-api-docs || echo 0)"

                            echo "🌐 Exposed ports:"
                            docker ps --format "table {{.Names}}\t{{.Ports}}" | grep -E "(eko-|traefik|grafana|loki)"
                        '''
                        echo "✅ Deployed with built-in auto-scaling"
                    } catch (Exception err) {
                        currentBuild.result = 'FAILURE'
                        throw err
                    }
                }
            }
        }
    }

    post {
        always {
            sh 'docker system prune -f'
        }
    }
}
