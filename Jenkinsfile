pipeline {
    agent any

    environment {
        EKO_API_REPLICAS = '10'        // General API instances
        EKO_DOCS_REPLICAS = '1'        // Document processing instances
        EXPOSED_PORT = '8200'
    }

    stages {
        stage('Environment Setup') {
            steps {
                withCredentials([file(credentialsId: 'echo_backend_env', variable: 'ENV_FILE')]) {
                    sh '''
                        echo "🔧 Setting up environment..."

                        # Use cat instead of cp to avoid permission issues
                        cat "$ENV_FILE" > .env || {
                            echo "Direct write failed, trying alternative..."
                            # If direct write fails, use docker to create the file
                            docker run --rm -v "$PWD":/workspace -v "$ENV_FILE":/source alpine:latest sh -c "
                                cat /source > /workspace/.env
                                echo 'EKO_API_REPLICAS=${EKO_API_REPLICAS}' >> /workspace/.env
                                echo 'EKO_DOCS_REPLICAS=${EKO_DOCS_REPLICAS}' >> /workspace/.env
                                echo 'EXPOSED_PORT=${EXPOSED_PORT}' >> /workspace/.env
                                chmod 644 /workspace/.env
                            "
                        } || {
                            # Last resort: create manually
                            echo "Creating .env manually..."
                            touch .env
                            echo "EKO_API_REPLICAS=${EKO_API_REPLICAS}" > .env
                            echo "EKO_DOCS_REPLICAS=${EKO_DOCS_REPLICAS}" >> .env
                            echo "EXPOSED_PORT=${EXPOSED_PORT}" >> .env
                        }

                        # Ensure required directories exist
                        mkdir -p logs config

                        echo "✅ Environment setup completed"
                        echo "📋 Configuration:"
                        echo "  - EKO API Replicas: ${EKO_API_REPLICAS}"
                        echo "  - EKO Docs Replicas: ${EKO_DOCS_REPLICAS}"
                        echo "  - Exposed Port: ${EXPOSED_PORT}"
                        ls -la .env || echo "Warning: .env file not found"
                    '''
                }
            }
        }

        stage('Stop Existing Services') {
            steps {
                sh '''
                    echo "🛑 Stopping existing services..."
                    docker compose down || true

                    echo "🧹 Cleaning up unused containers..."
                    docker container prune -f || true

                    echo "✅ Cleanup completed"
                '''
            }
        }

        stage('Build Images') {
            steps {
                sh '''
                    echo "🔨 Building Docker images..."
                    docker compose build --no-cache

                    echo "📦 Listing built images..."
                    docker images | grep eko-backend || echo "No eko-backend images found"

                    echo "✅ Build completed"
                '''
            }
        }

        stage('Deploy Services') {
            steps {
                sh '''
                    echo "🚀 Starting deployment with scaling..."
                    echo "  - EKO API instances: ${EKO_API_REPLICAS}"
                    echo "  - EKO Docs instances: ${EKO_DOCS_REPLICAS}"

                    # Start with scaling
                    docker compose up -d --scale eko-api=${EKO_API_REPLICAS} --scale eko-api-docs=${EKO_DOCS_REPLICAS}

                    echo "✅ Services started"
                '''
            }
        }

        stage('Health Check') {
            steps {
                sh '''
                    echo "⏳ Waiting for services to initialize..."
                    sleep 45

                    echo "🏥 Performing health checks..."

                    # Check if containers are running
                    echo "📊 Container status:"
                    docker compose ps

                    echo ""
                    echo "🔍 Instance verification:"
                    API_COUNT=$(docker ps --filter name=eko-api --filter status=running | grep -c eko-api || echo 0)
                    DOCS_COUNT=$(docker ps --filter name=eko-api-docs --filter status=running | grep -c eko-api-docs || echo 0)

                    echo "  - EKO API instances running: $API_COUNT"
                    echo "  - EKO Docs instances running: $DOCS_COUNT"

                    # Verify expected counts
                    if [ "$API_COUNT" -ne "${EKO_API_REPLICAS}" ]; then
                        echo "❌ Expected ${EKO_API_REPLICAS} API instances, but found $API_COUNT"
                        exit 1
                    fi

                    if [ "$DOCS_COUNT" -ne "${EKO_DOCS_REPLICAS}" ]; then
                        echo "❌ Expected ${EKO_DOCS_REPLICAS} Docs instances, but found $DOCS_COUNT"
                        exit 1
                    fi

                    echo "✅ All instances running as expected"
                '''
            }
        }

        stage('Verify Deployment') {
            steps {
                sh '''
                    echo "🌐 Deployment verification:"

                    echo "📡 Exposed ports:"
                    docker ps --format "table {{.Names}}\t{{.Ports}}" | grep -E "(eko-|traefik|grafana|loki)" || echo "No matching containers found"

                    echo ""
                    echo "🔗 Service endpoints:"
                    echo "  - API Documentation: http://localhost:8201"
                    echo "  - Traefik Dashboard: http://localhost:8202"
                    echo "  - Grafana Logs: http://localhost:8203"

                    echo ""
                    echo "📊 Resource usage:"
                    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | head -15

                    echo ""
                    echo "✅ Deployment verification completed"
                '''
            }
        }
    }

    post {
        always {
            script {
                echo "🧹 Post-deployment cleanup..."
                sh '''
                    # Clean up unused Docker resources
                    docker system prune -f

                    # Show final status
                    echo "📊 Final deployment status:"
                    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(eko-|traefik|grafana|loki)" || echo "No containers found"
                '''
            }
        }

        success {
            script {
                echo "🎉 Deployment completed successfully!"
                sh '''
                    echo "✅ SUCCESS: EKO Backend deployed with scaling"
                    echo "📈 Instances deployed:"
                    echo "  - EKO API: ${EKO_API_REPLICAS} instances"
                    echo "  - EKO Docs: ${EKO_DOCS_REPLICAS} instances"
                    echo ""
                    echo "🌐 Access points:"
                    echo "  - API Docs: http://localhost:8201"
                    echo "  - Traefik Dashboard: http://localhost:8202"
                    echo "  - Grafana Logs: http://localhost:8203"
                '''
            }
        }

        failure {
            script {
                echo "❌ Deployment failed!"
                sh '''
                    echo "🔍 Debugging information:"
                    echo "Container status:"
                    docker ps -a | grep eko || echo "No eko containers found"

                    echo ""
                    echo "Recent logs from failed containers:"
                    for container in $(docker ps -a --filter "status=exited" --format "{{.Names}}" | grep eko | head -3); do
                        echo "--- Logs from $container ---"
                        docker logs --tail 10 "$container" || echo "No logs available"
                        echo ""
                    done
                '''
            }
        }
    }
}
