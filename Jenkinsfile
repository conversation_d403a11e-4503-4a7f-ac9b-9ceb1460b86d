pipeline {
    agent any

    environment {
        EKO_API_REPLICAS = '10'        // General API instances
        EKO_DOCS_REPLICAS = '1'        // Document processing instances
        EXPOSED_PORT = '8200'
    }

    stages {
        stage('Environment Setup') {
            steps {
                withCredentials([file(credentialsId: 'echo_backend_env', variable: 'ENV_FILE')]) {
                    sh '''
                        echo "🔧 Setting up environment..."

                        # Use cat instead of cp to avoid permission issues
                        cat "$ENV_FILE" > .env || {
                            echo "Direct write failed, trying alternative..."
                            # If direct write fails, use docker to create the file
                            docker run --rm -v "$PWD":/workspace -v "$ENV_FILE":/source alpine:latest sh -c "
                                cat /source > /workspace/.env
                                echo 'EKO_API_REPLICAS=${EKO_API_REPLICAS}' >> /workspace/.env
                                echo 'EKO_DOCS_REPLICAS=${EKO_DOCS_REPLICAS}' >> /workspace/.env
                                echo 'EXPOSED_PORT=${EXPOSED_PORT}' >> /workspace/.env
                                chmod 644 /workspace/.env
                            "
                        } || {
                            # Last resort: create manually
                            echo "Creating .env manually..."
                            touch .env
                            echo "EKO_API_REPLICAS=${EKO_API_REPLICAS}" > .env
                            echo "EKO_DOCS_REPLICAS=${EKO_DOCS_REPLICAS}" >> .env
                            echo "EXPOSED_PORT=${EXPOSED_PORT}" >> .env
                        }

                        # Ensure required directories exist
                        mkdir -p logs config

                        echo "✅ Environment setup completed"
                        echo "📋 Configuration:"
                        echo "  - EKO API Replicas: ${EKO_API_REPLICAS}"
                        echo "  - EKO Docs Replicas: ${EKO_DOCS_REPLICAS}"
                        echo "  - Exposed Port: ${EXPOSED_PORT}"
                        ls -la .env || echo "Warning: .env file not found"
                    '''
                }
            }
        }

        stage('Stop Existing Services') {
            steps {
                sh '''
                    echo "🛑 Stopping existing services..."
                    docker compose down || true

                    echo "🧹 Cleaning up unused containers..."
                    docker container prune -f || true

                    echo "✅ Cleanup completed"
                '''
            }
        }

        stage('Build Base Image') {
            steps {
                sh '''
                    echo "🏗️ Building base Docker image..."

                    # Build the base image with timestamp tag
                    BUILD_TAG="eko-backend:build-${BUILD_NUMBER}-$(date +%Y%m%d-%H%M%S)"
                    echo "Building with tag: $BUILD_TAG"

                    # Build the image
                    docker build --no-cache -t eko-backend:latest -t "$BUILD_TAG" .

                    echo "📦 Base image built successfully:"
                    docker images | grep eko-backend | head -5

                    echo "🔍 Image details:"
                    docker inspect eko-backend:latest --format='{{.Size}}' | numfmt --to=iec

                    echo "✅ Base image build completed"
                '''
            }
        }

        stage('Validate Image') {
            steps {
                sh '''
                    echo "🧪 Validating built image..."

                    # Test if the image can start
                    echo "Testing image startup..."
                    CONTAINER_ID=$(docker run -d --name eko-test-${BUILD_NUMBER} eko-backend:latest)

                    # Wait a moment for startup
                    sleep 10

                    # Check if container is running
                    if docker ps | grep eko-test-${BUILD_NUMBER}; then
                        echo "✅ Image validation successful - container started"
                        docker logs eko-test-${BUILD_NUMBER} | tail -5
                    else
                        echo "❌ Image validation failed - container did not start"
                        docker logs eko-test-${BUILD_NUMBER} || echo "No logs available"
                        exit 1
                    fi

                    # Cleanup test container
                    docker stop eko-test-${BUILD_NUMBER} || true
                    docker rm eko-test-${BUILD_NUMBER} || true

                    echo "✅ Image validation completed"
                '''
            }
        }

        stage('Deploy Infrastructure') {
            steps {
                sh '''
                    echo "🏗️ Deploying infrastructure services..."

                    # Start infrastructure services first (Traefik, Loki, Grafana, Promtail)
                    echo "Starting Traefik reverse proxy..."
                    docker compose up -d traefik

                    echo "Starting logging stack..."
                    docker compose up -d loki grafana promtail

                    echo "⏳ Waiting for infrastructure to be ready..."
                    sleep 20

                    echo "📊 Infrastructure status:"
                    docker compose ps | grep -E "(traefik|loki|grafana|promtail)" || echo "No infrastructure services found"

                    echo "✅ Infrastructure deployment completed"
                '''
            }
        }

        stage('Deploy Application Services') {
            steps {
                sh '''
                    echo "🚀 Starting application deployment with scaling..."
                    echo "  - EKO API instances: ${EKO_API_REPLICAS}"
                    echo "  - EKO Docs instances: ${EKO_DOCS_REPLICAS}"
                    echo "  - Using pre-built image: eko-backend:latest"

                    # Verify the image exists before deployment
                    if ! docker images | grep "eko-backend.*latest"; then
                        echo "❌ Base image not found! Build stage may have failed."
                        exit 1
                    fi

                    echo "📋 Starting application services with scaling..."

                    # Start application services with scaling
                    docker compose up -d --scale eko-api=${EKO_API_REPLICAS} --scale eko-api-docs=${EKO_DOCS_REPLICAS} eko-api eko-api-docs

                    echo "⏳ Allowing application services to initialize..."
                    sleep 15

                    echo "📊 Application deployment status:"
                    docker compose ps | grep -E "(eko-api|eko-docs)" || echo "No eko services found"

                    echo "✅ Application services deployment completed"
                '''
            }
        }

        stage('Comprehensive Health Check') {
            steps {
                sh '''
                    echo "⏳ Final health check - waiting for all services..."
                    sleep 30

                    echo "🏥 Performing comprehensive health checks..."

                    # Check infrastructure services
                    echo "🏗️ Infrastructure Health:"
                    TRAEFIK_STATUS=$(docker ps --filter name=traefik --filter status=running | grep -c traefik || echo 0)
                    LOKI_STATUS=$(docker ps --filter name=loki --filter status=running | grep -c loki || echo 0)
                    GRAFANA_STATUS=$(docker ps --filter name=grafana --filter status=running | grep -c grafana || echo 0)
                    PROMTAIL_STATUS=$(docker ps --filter name=promtail --filter status=running | grep -c promtail || echo 0)

                    echo "  - Traefik: $TRAEFIK_STATUS/1"
                    echo "  - Loki: $LOKI_STATUS/1"
                    echo "  - Grafana: $GRAFANA_STATUS/1"
                    echo "  - Promtail: $PROMTAIL_STATUS/1"

                    # Check application services
                    echo ""
                    echo "🚀 Application Health:"
                    API_COUNT=$(docker ps --filter name=eko-api --filter status=running | grep -c eko-api || echo 0)
                    DOCS_COUNT=$(docker ps --filter name=eko-api-docs --filter status=running | grep -c eko-api-docs || echo 0)

                    echo "  - EKO API instances: $API_COUNT/${EKO_API_REPLICAS}"
                    echo "  - EKO Docs instances: $DOCS_COUNT/${EKO_DOCS_REPLICAS}"

                    # Verify infrastructure
                    INFRA_FAILED=0
                    if [ "$TRAEFIK_STATUS" -ne "1" ]; then
                        echo "❌ Traefik not running"
                        INFRA_FAILED=1
                    fi
                    if [ "$LOKI_STATUS" -ne "1" ]; then
                        echo "❌ Loki not running"
                        INFRA_FAILED=1
                    fi
                    if [ "$GRAFANA_STATUS" -ne "1" ]; then
                        echo "❌ Grafana not running"
                        INFRA_FAILED=1
                    fi

                    # Verify application scaling
                    APP_FAILED=0
                    if [ "$API_COUNT" -ne "${EKO_API_REPLICAS}" ]; then
                        echo "❌ Expected ${EKO_API_REPLICAS} API instances, but found $API_COUNT"
                        APP_FAILED=1
                    fi

                    if [ "$DOCS_COUNT" -ne "${EKO_DOCS_REPLICAS}" ]; then
                        echo "❌ Expected ${EKO_DOCS_REPLICAS} Docs instances, but found $DOCS_COUNT"
                        APP_FAILED=1
                    fi

                    # Overall health check
                    if [ "$INFRA_FAILED" -eq "1" ] || [ "$APP_FAILED" -eq "1" ]; then
                        echo ""
                        echo "❌ Health check failed - showing container status:"
                        docker compose ps
                        exit 1
                    fi

                    echo ""
                    echo "✅ All services healthy and running as expected"
                '''
            }
        }

        stage('Verify Deployment') {
            steps {
                sh '''
                    echo "🌐 Deployment verification:"

                    echo "📡 Exposed ports:"
                    docker ps --format "table {{.Names}}\t{{.Ports}}" | grep -E "(eko-|traefik|grafana|loki)" || echo "No matching containers found"

                    echo ""
                    echo "🔗 Service endpoints:"
                    echo "  - API Documentation: http://localhost:8201"
                    echo "  - Traefik Dashboard: http://localhost:8202"
                    echo "  - Grafana Logs: http://localhost:8203"

                    echo ""
                    echo "📊 Resource usage:"
                    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | head -15

                    echo ""
                    echo "✅ Deployment verification completed"
                '''
            }
        }
    }

    post {
        always {
            script {
                echo "🧹 Post-deployment cleanup..."
                sh '''
                    # Clean up unused Docker resources
                    docker system prune -f

                    # Show final status
                    echo "📊 Final deployment status:"
                    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(eko-|traefik|grafana|loki)" || echo "No containers found"
                '''
            }
        }

        success {
            script {
                echo "🎉 Deployment completed successfully!"
                sh '''
                    echo "✅ SUCCESS: EKO Backend deployed with scaling"
                    echo "📈 Instances deployed:"
                    echo "  - EKO API: ${EKO_API_REPLICAS} instances"
                    echo "  - EKO Docs: ${EKO_DOCS_REPLICAS} instances"
                    echo ""
                    echo "🌐 Access points:"
                    echo "  - API Docs: http://localhost:8201"
                    echo "  - Traefik Dashboard: http://localhost:8202"
                    echo "  - Grafana Logs: http://localhost:8203"
                '''
            }
        }

        failure {
            script {
                echo "❌ Deployment failed!"
                sh '''
                    echo "🔍 Debugging information:"
                    echo "Container status:"
                    docker ps -a | grep eko || echo "No eko containers found"

                    echo ""
                    echo "Recent logs from failed containers:"
                    for container in $(docker ps -a --filter "status=exited" --format "{{.Names}}" | grep eko | head -3); do
                        echo "--- Logs from $container ---"
                        docker logs --tail 10 "$container" || echo "No logs available"
                        echo ""
                    done
                '''
            }
        }
    }
}
