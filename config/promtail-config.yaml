server:
  http_listen_port: 9080
  grpc_listen_port: 0
  log_level: warn

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # Real-time Docker logs to Loki + Daily Files
  - job_name: docker
    static_configs:
      - targets:
          - localhost
        labels:
          job: docker
          __path__: /var/lib/docker/containers/*/*-json.log
    
    pipeline_stages:
      - json:
          expressions:
            output: log
            stream: stream
            attrs: attrs
            time: time
      
      - json:
          expressions:
            tag: tag
          source: attrs
      
      - regex:
          expression: (?P<container_name>(?:[^|]*))
          source: tag
      
      - labels:
          stream:
          container_name:
      
      # Filter only eko services
      - match:
          selector: '{container_name=~".*eko-api.*|.*traefik.*"}'
          action: keep
      
      - timestamp:
          source: time
          format: RFC3339Nano
      
      # Real-time file output with daily rotation
      - template:
          source: daily_file
          template: '/var/log/eko/eko-{{ .timestamp | date "2006-01-02" }}.log'
      
      # Format like Docker logs: container_name | log_content
      - template:
          source: file_content
          template: '{{ .container_name }} | {{ .output }}'
      
      # Write to daily file in real-time
      - match:
          selector: '{job="docker"}'
          stages:
            - output:
                source: file_content
