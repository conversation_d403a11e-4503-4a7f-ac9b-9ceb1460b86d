version: '3.8'

# Centralized Logging Stack with Loki
# Usage: docker-compose -f docker-compose.yml -f docker-compose.logging.yml up -d

services:
  # Loki - Log aggregation system
  loki:
    image: grafana/loki:2.9.0
    container_name: loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./logs/loki:/loki
      - ./config/loki-config.yaml:/etc/loki/local-config.yaml
    networks:
      - eko-network

  # Promtail - Log collector
  promtail:
    image: grafana/promtail:2.9.0
    container_name: promtail
    restart: unless-stopped
    volumes:
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - ./config/promtail-config.yaml:/etc/promtail/config.yml
    command: -config.file=/etc/promtail/config.yml
    networks:
      - eko-network
    depends_on:
      - loki

  # Grafana - Log visualization (optional)
  grafana:
    image: grafana/grafana:10.0.0
    container_name: grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana
      - ./config/grafana-datasources.yaml:/etc/grafana/provisioning/datasources/datasources.yaml
    networks:
      - eko-network
    depends_on:
      - loki

volumes:
  grafana-storage:

networks:
  eko-network:
    external: true
