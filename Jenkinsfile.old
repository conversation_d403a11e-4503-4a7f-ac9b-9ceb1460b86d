// pipeline {
//     agent any

//     environment {
//         EKO_API_REPLICAS = '10'        // General API instances
//         EKO_DOCS_REPLICAS = '1'        // Document processing instances
//         EXPOSED_PORT = '8200'
//     }

//     stages {
//         stage('Environment Setup') {
//             steps {
//                 withCredentials([file(credentialsId: 'echo_backend_env', variable: 'ENV_FILE')]) {
//                     sh '''
//                         echo "🔧 Setting up environment..."

//                         # Use cat instead of cp to avoid permission issues
//                         cat "$ENV_FILE" > .env || {
//                             echo "Direct write failed, trying alternative..."
//                             # If direct write fails, use docker to create the file
//                             docker run --rm -v "$PWD":/workspace -v "$ENV_FILE":/source alpine:latest sh -c "
//                                 cat /source > /workspace/.env
//                                 echo 'EKO_API_REPLICAS=${EKO_API_REPLICAS}' >> /workspace/.env
//                                 echo 'EKO_DOCS_REPLICAS=${EKO_DOCS_REPLICAS}' >> /workspace/.env
//                                 echo 'EXPOSED_PORT=${EXPOSED_PORT}' >> /workspace/.env
//                                 chmod 644 /workspace/.env
//                             "
//                         } || {
//                             # Last resort: create manually
//                             echo "Creating .env manually..."
//                             touch .env
//                             echo "EKO_API_REPLICAS=${EKO_API_REPLICAS}" > .env
//                             echo "EKO_DOCS_REPLICAS=${EKO_DOCS_REPLICAS}" >> .env
//                             echo "EXPOSED_PORT=${EXPOSED_PORT}" >> .env
//                         }

//                         # Ensure required directories exist
//                         mkdir -p logs config

//                         echo "✅ Environment setup completed"
//                         echo "📋 Configuration:"
//                         echo "  - EKO API Replicas: ${EKO_API_REPLICAS}"
//                         echo "  - EKO Docs Replicas: ${EKO_DOCS_REPLICAS}"
//                         echo "  - Exposed Port: ${EXPOSED_PORT}"
//                         ls -la .env || echo "Warning: .env file not found"
//                     '''
//                 }
//             }
//         }

//         stage('Stop Existing Services') {
//             steps {
//                 sh '''
//                     echo "🛑 Stopping existing services..."
//                     docker compose down || true

//                     echo "🧹 Cleaning up unused containers..."
//                     docker container prune -f || true

//                     echo "✅ Cleanup completed"
//                 '''
//             }
//         }

//         stage('Check Port Availability') {
//             steps {
//                 sh '''
//                     echo "🔍 Checking port availability..."

//                     # Check critical ports
//                     PORTS_TO_CHECK="80 443 8201 8202 8203 3100"
//                     CONFLICTS_FOUND=0

//                     for port in $PORTS_TO_CHECK; do
//                         if netstat -tuln | grep ":$port " >/dev/null 2>&1; then
//                             echo "⚠️  Port $port is in use:"
//                             netstat -tuln | grep ":$port " || true
//                             lsof -i :$port || true
//                             CONFLICTS_FOUND=1
//                         else
//                             echo "✅ Port $port is available"
//                         fi
//                     done

//                     if [ "$CONFLICTS_FOUND" -eq "1" ]; then
//                         echo ""
//                         echo "🔧 Attempting to resolve port conflicts..."

//                         # Stop any existing Docker containers using these ports
//                         echo "Stopping containers that might be using conflicting ports..."
//                         docker ps --format "{{.Names}}" | xargs -r docker stop || true

//                         # Wait a moment for ports to be released
//                         sleep 5

//                         # Check again
//                         echo "Rechecking port availability..."
//                         STILL_CONFLICTS=0
//                         for port in $PORTS_TO_CHECK; do
//                             if netstat -tuln | grep ":$port " >/dev/null 2>&1; then
//                                 echo "❌ Port $port still in use after cleanup"
//                                 STILL_CONFLICTS=1
//                             fi
//                         done

//                         if [ "$STILL_CONFLICTS" -eq "1" ]; then
//                             echo ""
//                             echo "❌ Port conflicts remain. Manual intervention required."
//                             echo "Please check what services are using these ports:"
//                             netstat -tuln | grep -E ":(80|443|8201|8202|8203|3100) " || true
//                             exit 1
//                         fi
//                     fi

//                     echo "✅ All required ports are available"
//                 '''
//             }
//         }

//         stage('Build Base Image') {
//             steps {
//                 sh '''
//                     echo "🏗️ Building base Docker image..."

//                     # Build the base image with timestamp tag
//                     BUILD_TAG="eko-backend:build-${BUILD_NUMBER}-$(date +%Y%m%d-%H%M%S)"
//                     echo "Building with tag: $BUILD_TAG"

//                     # Build the image
//                     docker build --no-cache -t eko-backend:latest -t "$BUILD_TAG" .

//                     echo "📦 Base image built successfully:"
//                     docker images | grep eko-backend | head -5

//                     echo "🔍 Image details:"
//                     docker inspect eko-backend:latest --format='{{.Size}}' | numfmt --to=iec

//                     echo "✅ Base image build completed"
//                 '''
//             }
//         }

//         stage('Validate Image') {
//             steps {
//                 sh '''
//                     echo "🧪 Validating built image..."

//                     # Test if the image can start
//                     echo "Testing image startup..."
//                     CONTAINER_ID=$(docker run -d --name eko-test-${BUILD_NUMBER} eko-backend:latest)

//                     # Wait a moment for startup
//                     sleep 10

//                     # Check if container is running
//                     if docker ps | grep eko-test-${BUILD_NUMBER}; then
//                         echo "✅ Image validation successful - container started"
//                         docker logs eko-test-${BUILD_NUMBER} | tail -5
//                     else
//                         echo "❌ Image validation failed - container did not start"
//                         docker logs eko-test-${BUILD_NUMBER} || echo "No logs available"
//                         exit 1
//                     fi

//                     # Cleanup test container
//                     docker stop eko-test-${BUILD_NUMBER} || true
//                     docker rm eko-test-${BUILD_NUMBER} || true

//                     echo "✅ Image validation completed"
//                 '''
//             }
//         }

//         stage('Deploy Infrastructure') {
//             steps {
//                 script {
//                     try {
//                         sh '''
//                             echo "🏗️ Deploying infrastructure services..."

//                             # Start logging stack first (no port conflicts)
//                             echo "Starting logging stack..."
//                             docker compose up -d loki grafana promtail

//                             echo "⏳ Waiting for logging stack..."
//                             sleep 15

//                             # Start Traefik (most likely to have port conflicts)
//                             echo "Starting Traefik reverse proxy..."
//                             if docker compose up -d traefik; then
//                                 echo "✅ Traefik started successfully"
//                             else
//                                 echo "⚠️  Traefik failed to start (likely port 80/443 conflict)"
//                                 echo "🔍 Checking what's using ports 80 and 443:"
//                                 netstat -tuln | grep -E ":(80|443) " || echo "Port check failed"
//                                 echo ""
//                                 echo "📝 Note: Continuing deployment without Traefik"
//                                 echo "    Services will be accessible via direct ports:"
//                                 echo "    - API: http://server:8201"
//                                 echo "    - Grafana: http://server:8203"
//                             fi

//                             echo "⏳ Waiting for infrastructure to be ready..."
//                             sleep 20

//                             echo "📊 Infrastructure status:"
//                             docker compose ps | grep -E "(traefik|loki|grafana|promtail)" || echo "No infrastructure services found"

//                             echo "✅ Infrastructure deployment completed"
//                         '''
//                     } catch (Exception err) {
//                         echo "❌ Infrastructure deployment failed, attempting recovery..."
//                         sh '''
//                             echo "� Attempting alternative deployment strategy..."

//                             # Check what failed
//                             echo "Current container status:"
//                             docker compose ps || true

//                             # Try to start services individually
//                             echo "Starting services individually..."

//                             # Start logging services (usually no conflicts)
//                             docker compose up -d loki || echo "Loki failed to start"
//                             docker compose up -d grafana || echo "Grafana failed to start"
//                             docker compose up -d promtail || echo "Promtail failed to start"

//                             # For Traefik, show detailed error if it fails
//                             if ! docker compose up -d traefik; then
//                                 echo "❌ Traefik failed to start. Checking logs..."
//                                 docker compose logs traefik || true
//                                 echo ""
//                                 echo "🔍 Port usage analysis:"
//                                 netstat -tuln | grep -E ":(80|443|8201|8202)" || echo "No conflicts found"
//                                 echo ""
//                                 echo "⚠️  Continuing without Traefik - direct port access will be used"
//                             fi

//                             echo "📊 Final infrastructure status:"
//                             docker compose ps | grep -E "(loki|grafana|promtail)" || echo "No services running"
//                         '''
//                     }
//                 }
//             }
//         }

//         stage('Deploy Application Services') {
//             steps {
//                 sh '''
//                     echo "🚀 Starting application deployment with scaling..."
//                     echo "  - EKO API instances: ${EKO_API_REPLICAS}"
//                     echo "  - EKO Docs instances: ${EKO_DOCS_REPLICAS}"
//                     echo "  - Using pre-built image: eko-backend:latest"

//                     # Verify the image exists before deployment
//                     if ! docker images | grep "eko-backend.*latest"; then
//                         echo "❌ Base image not found! Build stage may have failed."
//                         exit 1
//                     fi

//                     echo "📋 Starting application services with scaling..."

//                     # Start application services with scaling
//                     docker compose up -d --scale eko-api=${EKO_API_REPLICAS} --scale eko-api-docs=${EKO_DOCS_REPLICAS} eko-api eko-api-docs

//                     echo "⏳ Allowing application services to initialize..."
//                     sleep 15

//                     echo "📊 Application deployment status:"
//                     docker compose ps | grep -E "(eko-api|eko-docs)" || echo "No eko services found"

//                     echo "✅ Application services deployment completed"
//                 '''
//             }
//         }

//         stage('Comprehensive Health Check') {
//             steps {
//                 sh '''
//                     echo "⏳ Final health check - waiting for all services..."
//                     sleep 30

//                     echo "🏥 Performing comprehensive health checks..."

//                     # Check infrastructure services
//                     echo "🏗️ Infrastructure Health:"
//                     TRAEFIK_STATUS=$(docker ps --filter name=traefik --filter status=running | grep -c traefik || echo 0)
//                     LOKI_STATUS=$(docker ps --filter name=loki --filter status=running | grep -c loki || echo 0)
//                     GRAFANA_STATUS=$(docker ps --filter name=grafana --filter status=running | grep -c grafana || echo 0)
//                     PROMTAIL_STATUS=$(docker ps --filter name=promtail --filter status=running | grep -c promtail || echo 0)

//                     echo "  - Traefik: $TRAEFIK_STATUS/1"
//                     echo "  - Loki: $LOKI_STATUS/1"
//                     echo "  - Grafana: $GRAFANA_STATUS/1"
//                     echo "  - Promtail: $PROMTAIL_STATUS/1"

//                     # Check application services
//                     echo ""
//                     echo "🚀 Application Health:"
//                     API_COUNT=$(docker ps --filter name=eko-api --filter status=running | grep -c eko-api || echo 0)
//                     DOCS_COUNT=$(docker ps --filter name=eko-api-docs --filter status=running | grep -c eko-api-docs || echo 0)

//                     echo "  - EKO API instances: $API_COUNT/${EKO_API_REPLICAS}"
//                     echo "  - EKO Docs instances: $DOCS_COUNT/${EKO_DOCS_REPLICAS}"

//                     # Verify infrastructure (Traefik is optional due to port conflicts)
//                     INFRA_FAILED=0
//                     if [ "$TRAEFIK_STATUS" -ne "1" ]; then
//                         echo "⚠️  Traefik not running (acceptable if port conflicts exist)"
//                     else
//                         echo "✅ Traefik running successfully"
//                     fi

//                     if [ "$LOKI_STATUS" -ne "1" ]; then
//                         echo "❌ Loki not running - CRITICAL"
//                         INFRA_FAILED=1
//                     fi
//                     if [ "$GRAFANA_STATUS" -ne "1" ]; then
//                         echo "❌ Grafana not running - CRITICAL"
//                         INFRA_FAILED=1
//                     fi

//                     # Verify application scaling
//                     APP_FAILED=0
//                     if [ "$API_COUNT" -ne "${EKO_API_REPLICAS}" ]; then
//                         echo "❌ Expected ${EKO_API_REPLICAS} API instances, but found $API_COUNT"
//                         APP_FAILED=1
//                     fi

//                     if [ "$DOCS_COUNT" -ne "${EKO_DOCS_REPLICAS}" ]; then
//                         echo "❌ Expected ${EKO_DOCS_REPLICAS} Docs instances, but found $DOCS_COUNT"
//                         APP_FAILED=1
//                     fi

//                     # Overall health check
//                     if [ "$INFRA_FAILED" -eq "1" ] || [ "$APP_FAILED" -eq "1" ]; then
//                         echo ""
//                         echo "❌ Health check failed - showing container status:"
//                         docker compose ps
//                         exit 1
//                     fi

//                     echo ""
//                     echo "✅ All services healthy and running as expected"
//                 '''
//             }
//         }

//         stage('Verify Deployment') {
//             steps {
//                 sh '''
//                     echo "🌐 Deployment verification:"

//                     echo "📡 Exposed ports:"
//                     docker ps --format "table {{.Names}}\t{{.Ports}}" | grep -E "(eko-|traefik|grafana|loki)" || echo "No matching containers found"

//                     echo ""
//                     echo "🔗 Service endpoints:"
//                     echo "  - API Documentation: http://localhost:8201"
//                     echo "  - Traefik Dashboard: http://localhost:8202"
//                     echo "  - Grafana Logs: http://localhost:8203"

//                     echo ""
//                     echo "📊 Resource usage:"
//                     docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | head -15

//                     echo ""
//                     echo "✅ Deployment verification completed"
//                 '''
//             }
//         }
//     }

//     post {
//         always {
//             script {
//                 echo "🧹 Post-deployment cleanup..."
//                 sh '''
//                     # Clean up unused Docker resources
//                     docker system prune -f

//                     # Show final status
//                     echo "📊 Final deployment status:"
//                     docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(eko-|traefik|grafana|loki)" || echo "No containers found"
//                 '''
//             }
//         }

//         success {
//             script {
//                 echo "🎉 Deployment completed successfully!"
//                 sh '''
//                     echo "✅ SUCCESS: EKO Backend deployed with scaling"
//                     echo "📈 Instances deployed:"
//                     echo "  - EKO API: ${EKO_API_REPLICAS} instances"
//                     echo "  - EKO Docs: ${EKO_DOCS_REPLICAS} instances"
//                     echo ""
//                     echo "🌐 Access points:"
//                     echo "  - Main HTTP: http://localhost:8080"
//                     echo "  - Main HTTPS: https://localhost:8443"
//                     echo "  - API Docs: http://localhost:8201"
//                     echo "  - Traefik Dashboard: http://localhost:8202"
//                     echo "  - Grafana Logs: http://localhost:8203"
//                 '''
//             }
//         }

//         failure {
//             script {
//                 echo "❌ Deployment failed!"
//                 sh '''
//                     echo "🔍 Debugging information:"
//                     echo "Container status:"
//                     docker ps -a | grep eko || echo "No eko containers found"

//                     echo ""
//                     echo "Recent logs from failed containers:"
//                     for container in $(docker ps -a --filter "status=exited" --format "{{.Names}}" | grep eko | head -3); do
//                         echo "--- Logs from $container ---"
//                         docker logs --tail 10 "$container" || echo "No logs available"
//                         echo ""
//                     done
//                 '''
//             }
//         }
//     }
// }
