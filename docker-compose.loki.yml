# Advanced logging with <PERSON> + <PERSON><PERSON>
# Usage: docker-compose -f docker-compose.yml -f docker-compose.loki.yml up -d

version: '3.8'

services:
  # Loki - Log aggregation
  loki:
    image: grafana/loki:2.9.0
    container_name: loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - loki-data:/loki
    networks:
      - eko-network

  # Grafana - Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
    networks:
      - eko-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.grafana.rule=Host(`${DOMAIN}`) && PathPrefix(`/grafana`)"
      - "traefik.http.routers.grafana.entrypoints=websecure,http-api"
      - "traefik.http.routers.grafana.service=grafana-service"
      - "${DOMAIN:+traefik.http.routers.grafana.tls=true}"
      - "traefik.http.services.grafana-service.loadbalancer.server.port=3000"

  # Promtail - Log collector
  promtail:
    image: grafana/promtail:2.9.0
    container_name: promtail
    restart: unless-stopped
    volumes:
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    command: |
      -config.file=/etc/promtail/config.yml
      -config.expand-env=true
    environment:
      - LOKI_URL=http://loki:3100/loki/api/v1/push
    networks:
      - eko-network
    configs:
      - source: promtail_config
        target: /etc/promtail/config.yml

configs:
  promtail_config:
    content: |
      server:
        http_listen_port: 9080
        grpc_listen_port: 0

      positions:
        filename: /tmp/positions.yaml

      clients:
        - url: http://loki:3100/loki/api/v1/push

      scrape_configs:
        - job_name: containers
          static_configs:
            - targets:
                - localhost
              labels:
                job: containerlogs
                __path__: /var/lib/docker/containers/*/*log

          pipeline_stages:
            - json:
                expressions:
                  output: log
                  stream: stream
                  attrs:
            - json:
                expressions:
                  tag:
                source: attrs
            - regex:
                expression: (?P<container_name>(?:[^|]*))\|
                source: tag
            - timestamp:
                format: RFC3339Nano
                source: time
            - labels:
                stream:
                container_name:
            - match:
                selector: '{container_name=~"eko-api.*|traefik-api"}'
                action: keep
            - output:
                source: output

volumes:
  loki-data:
  grafana-data:

networks:
  eko-network:
    external: true
