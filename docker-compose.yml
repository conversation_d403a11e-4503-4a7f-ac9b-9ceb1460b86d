---
# Traefik Configuration with Multiple Instances and Sticky Sessions
# Supports WebSocket connections and document processing with session affinity

services:
  traefik:
    container_name: traefik-api
    image: traefik:3.0.1
    restart: unless-stopped
    ports:
      - "80:80"       # HTTP
      - "443:443"     # HTTPS
      - "8201:8201"   # API docs port
      - "8202:8202"   # Traefik dashboard port
    volumes:
      # Docker socket for service discovery
      - /var/run/docker.sock:/var/run/docker.sock:ro
      # Traefik configuration
      - ./config/traefik.yaml:/etc/traefik/traefik.yaml:ro
      - ./config/conf/:/etc/traefik/conf/
      # Use existing Let's Encrypt certificates - mount entire letsencrypt directory to resolve symlinks
      - /etc/letsencrypt/:/etc/letsencrypt/:ro
    environment:
      - DOMAIN=${DOMAIN:-localhost}
    command:
      - /bin/sh
      - -c
      - |
        echo "🌐 DOMAIN: $$DOMAIN"
        echo "📜 Using existing Let's Encrypt certificates from /etc/letsencrypt/live/$$DOMAIN/"
        echo "🔍 Checking certificate files..."

        CERT_DIR="/etc/letsencrypt/live/$$DOMAIN"
        if [ -d "$$CERT_DIR" ]; then
          echo "✅ Certificate directory found: $$CERT_DIR"
          ls -la "$$CERT_DIR"

          if [ -f "$$CERT_DIR/fullchain.pem" ]; then
            echo "✅ fullchain.pem found"
            openssl x509 -in "$$CERT_DIR/fullchain.pem" -text -noout | grep -E "(Subject:|Issuer:|Not After)" || echo "❌ Cannot read certificate"
          else
            echo "❌ fullchain.pem not found"
          fi

          if [ -f "$$CERT_DIR/privkey.pem" ]; then
            echo "✅ privkey.pem found"
          else
            echo "❌ privkey.pem not found"
          fi

          # Generate dynamic TLS configuration
          echo "🔧 Generating dynamic TLS configuration..."
          cat > /etc/traefik/conf/dynamic-tls.yaml << EOF
        # Dynamic TLS Configuration for $$DOMAIN
        tls:
          certificates:
            - certFile: $$CERT_DIR/fullchain.pem
              keyFile: $$CERT_DIR/privkey.pem
        EOF
          echo "✅ Dynamic TLS configuration created"
        else
          echo "❌ Certificate directory not found: $$CERT_DIR"
        fi

        echo "🚀 Starting Traefik..."
        traefik --configfile=/etc/traefik/traefik.yaml
    networks:
      - eko-network
    labels:
      - "traefik.enable=true"

      # HTTP to HTTPS redirect - only if domain is provided
      - "${DOMAIN:+traefik.http.routers.http-redirect.rule=Host(`${DOMAIN}`)}"
      - "${DOMAIN:+traefik.http.routers.http-redirect.entrypoints=web}"
      - "${DOMAIN:+traefik.http.routers.http-redirect.middlewares=https-redirect@file}"

      # Traefik dashboard on port 8202 - works with or without domain
      - "traefik.http.routers.traefik-dashboard.rule=${DOMAIN:+Host(`${DOMAIN}`)}${DOMAIN:+ || }PathPrefix(`/`)"
      - "traefik.http.routers.traefik-dashboard.entrypoints=traefik-dashboard"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"
      - "traefik.http.routers.traefik-dashboard.middlewares=api-cors@file"

  # Dedicated Document Processing Instances (1-2 instances for WebSocket + process-documents)
  # These instances handle ONLY document processing flow with guaranteed sticky sessions
  eko-api-docs:
    build: .
    image: eko-backend:latest
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend-docs
      - INSTANCE_ID=${HOSTNAME:-eko-api-docs}
      - INSTANCE_TYPE=document-processing
      - TZ=Asia/Kathmandu
    volumes:
      - /etc/hosts:/etc/hosts:ro
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - eko-network
    labels:
      - "traefik.enable=true"

      # CRITICAL: Document processing endpoints - DEDICATED instances only
      # Process documents endpoint - highest priority, dedicated service
      - "traefik.http.routers.process-docs.rule=${DOMAIN:+Host(`${DOMAIN}`) && }PathPrefix(`/process-documents`)"
      - "traefik.http.routers.process-docs.entrypoints=websecure,http-api"
      - "traefik.http.routers.process-docs.service=eko-docs-service"
      - "traefik.http.routers.process-docs.middlewares=api-cors@file,document-headers@file"
      - "traefik.http.routers.process-docs.priority=100"
      - "${DOMAIN:+traefik.http.routers.process-docs.tls=true}"

      # WebSocket setup_files endpoint - SAME dedicated instances as process-documents
      - "traefik.http.routers.websocket.rule=${DOMAIN:+Host(`${DOMAIN}`) && }PathPrefix(`/setup_files`)"
      - "traefik.http.routers.websocket.entrypoints=websecure,http-api"
      - "traefik.http.routers.websocket.service=eko-docs-service"
      - "traefik.http.routers.websocket.middlewares=websocket-headers@file,api-cors@file"
      - "traefik.http.routers.websocket.priority=99"
      - "${DOMAIN:+traefik.http.routers.websocket.tls=true}"

      # Check status endpoint - SAME dedicated instances
      - "traefik.http.routers.check-status.rule=${DOMAIN:+Host(`${DOMAIN}`) && }PathPrefix(`/check_status`)"
      - "traefik.http.routers.check-status.entrypoints=websecure,http-api"
      - "traefik.http.routers.check-status.service=eko-docs-service"
      - "traefik.http.routers.check-status.middlewares=api-cors@file"
      - "traefik.http.routers.check-status.priority=98"
      - "${DOMAIN:+traefik.http.routers.check-status.tls=true}"

      # Dedicated service configuration with sticky sessions for document processing
      - "traefik.http.services.eko-docs-service.loadbalancer.server.port=8000"
      - "traefik.http.services.eko-docs-service.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.eko-docs-service.loadbalancer.healthcheck.interval=30s"
      - "traefik.http.services.eko-docs-service.loadbalancer.healthcheck.timeout=10s"
      - "traefik.http.services.eko-docs-service.loadbalancer.healthcheck.scheme=http"

      # Session stickiness with cookies - CRITICAL for document processing flow
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie.name=eko-docs-session"
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie.secure=false"
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie.httpOnly=false"
      - "traefik.http.services.eko-docs-service.loadbalancer.sticky.cookie.sameSite=none"

      # Load balancer strategy - ensures consistent routing
      - "traefik.http.services.eko-docs-service.loadbalancer.passhostheader=true"

  # General API Instances (4+ instances for all other traffic)
  # Use: docker-compose up --scale eko-api=4 --scale eko-api-docs=1 -d
  eko-api:
    build: .
    image: eko-backend:latest
    restart: unless-stopped
    environment:
      - PORT=8000
      - SERVICE_NAME=eko-backend
      - INSTANCE_ID=${HOSTNAME:-eko-api}
      - INSTANCE_TYPE=general
      - TZ=Asia/Kathmandu
    volumes:
      - /etc/hosts:/etc/hosts:ro
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - eko-network
    labels:
      - "traefik.enable=true"

      # Main HTTPS API routes - only if domain is provided
      - "${DOMAIN:+traefik.http.routers.api-secure.rule=Host(`${DOMAIN}`)}"
      - "${DOMAIN:+traefik.http.routers.api-secure.entrypoints=websecure}"
      - "${DOMAIN:+traefik.http.routers.api-secure.tls=true}"
      - "${DOMAIN:+traefik.http.routers.api-secure.service=eko-api-service}"
      - "${DOMAIN:+traefik.http.routers.api-secure.middlewares=default-headers@file,api-cors@file}"

      # General API routes - handles all traffic EXCEPT document processing
      - "traefik.http.routers.api-docs.rule=${DOMAIN:+Host(`${DOMAIN}`)}${DOMAIN:+ || }PathPrefix(`/`)"
      - "traefik.http.routers.api-docs.entrypoints=http-api"
      - "traefik.http.routers.api-docs.service=eko-api-service"
      - "traefik.http.routers.api-docs.middlewares=api-cors@file,compression@file"
      - "traefik.http.routers.api-docs.priority=1"

      # General service configuration - handles all non-document processing traffic
      - "traefik.http.services.eko-api-service.loadbalancer.server.port=8000"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.interval=30s"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.timeout=10s"
      - "traefik.http.services.eko-api-service.loadbalancer.healthcheck.scheme=http"

      # Load balancer strategy for general traffic
      - "traefik.http.services.eko-api-service.loadbalancer.passhostheader=true"

  # Loki - Log aggregation with daily rotation
  loki:
    image: grafana/loki:2.9.0-amd64
    container_name: eko-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./logs:/var/log/eko
      - loki-data:/loki
      - ./config/loki-config.yaml:/etc/loki/local-config.yaml:ro
    networks:
      - eko-network
    mem_limit: 100m
    cpus: 0.2

  # Grafana - Dashboard on port 8203
  grafana:
    image: grafana/grafana:10.2.0-ubuntu
    container_name: eko-grafana
    restart: unless-stopped
    ports:
      - "8203:3000"  # Your requested port
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_ANALYTICS_REPORTING_ENABLED=false
      - GF_ANALYTICS_CHECK_FOR_UPDATES=false
    volumes:
      - grafana-data:/var/lib/grafana
    networks:
      - eko-network
    mem_limit: 80m
    cpus: 0.1
    depends_on:
      - loki

  # Promtail - Real-time log collector
  promtail:
    image: grafana/promtail:2.9.0-amd64
    container_name: eko-promtail
    restart: unless-stopped
    volumes:
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./logs:/var/log/eko
      - ./config/promtail-config.yaml:/etc/promtail/config.yml:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - eko-network
    mem_limit: 50m
    cpus: 0.1
    depends_on:
      - loki

volumes:
  loki-data:
  grafana-data:

networks:
  eko-network:
    driver: bridge


